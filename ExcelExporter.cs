using System;
using System.Data;
using System.IO;
using System.Text;

namespace SqlToExcel
{
    public class ExcelExporter
    {
        /// <summary>
        /// 将DataTable导出为CSV文件（可用Excel打开）
        /// </summary>
        /// <param name="dataTable">要导出的数据表</param>
        /// <param name="filePath">保存文件路径</param>
        /// <param name="progressCallback">进度回调函数</param>
        public static void ExportToExcel(DataTable dataTable, string filePath, Action<int> progressCallback = null)
        {
            // 如果文件扩展名是.xlsx，改为.csv
            if (Path.GetExtension(filePath).ToLower() == ".xlsx")
            {
                filePath = Path.ChangeExtension(filePath, ".csv");
            }

            using (StreamWriter writer = new StreamWriter(filePath, false, Encoding.UTF8))
            {
                // 写入BOM以支持中文
                writer.Write('\uFEFF');

                // 写入列标题
                string[] columnNames = new string[dataTable.Columns.Count];
                for (int col = 0; col < dataTable.Columns.Count; col++)
                {
                    columnNames[col] = EscapeCsvField(dataTable.Columns[col].ColumnName);
                }
                writer.WriteLine(string.Join(",", columnNames));

                // 写入数据行
                int totalRows = dataTable.Rows.Count;
                for (int row = 0; row < totalRows; row++)
                {
                    string[] values = new string[dataTable.Columns.Count];
                    for (int col = 0; col < dataTable.Columns.Count; col++)
                    {
                        object value = dataTable.Rows[row][col];

                        // 根据需求处理不同的数据类型
                        if (value == null || value == DBNull.Value)
                        {
                            values[col] = "NULL";
                        }
                        else if (value is string stringValue)
                        {
                            // 空字符串保持为空
                            values[col] = EscapeCsvField(stringValue);
                        }
                        else if (value is DateTime dateValue)
                        {
                            // 日期时间类型
                            values[col] = EscapeCsvField(dateValue.ToString("yyyy-MM-dd HH:mm:ss"));
                        }
                        else if (value is bool boolValue)
                        {
                            // 布尔类型
                            values[col] = boolValue.ToString();
                        }
                        else
                        {
                            // 其他类型转换为字符串
                            values[col] = EscapeCsvField(value.ToString());
                        }
                    }
                    writer.WriteLine(string.Join(",", values));

                    // 报告进度
                    if (progressCallback != null && row % 100 == 0)
                    {
                        int progress = (int)((double)(row + 1) / totalRows * 100);
                        progressCallback(progress);
                    }
                }

                // 完成进度
                progressCallback?.Invoke(100);
            }
        }

        /// <summary>
        /// 转义CSV字段
        /// </summary>
        /// <param name="field">字段值</param>
        /// <returns>转义后的字段值</returns>
        private static string EscapeCsvField(string field)
        {
            if (string.IsNullOrEmpty(field))
                return "";

            // 如果包含逗号、引号或换行符，需要用引号包围并转义内部引号
            if (field.Contains(",") || field.Contains("\"") || field.Contains("\n") || field.Contains("\r"))
            {
                return "\"" + field.Replace("\"", "\"\"") + "\"";
            }

            return field;
        }
    }
}
