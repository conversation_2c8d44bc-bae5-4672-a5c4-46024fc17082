SQL查询导出Excel工具 - 使用说明
=====================================

程序文件：SqlToExcel.exe

功能说明：
---------
这是一个通用的SQL Server数据库查询工具，可以执行任意SELECT语句并将结果导出为CSV文件（可用Excel打开）或生成INSERT脚本。

主要特点：
---------
1. 支持SQL Server数据库连接（Windows身份验证和SQL Server身份验证）
2. 支持执行任意SELECT查询语句
3. 数据格式严格按照要求处理：
   - 空字符串显示为空
   - 数字0显示为0
   - NULL值显示为"NULL"
4. 导出CSV格式文件，可直接用Excel打开
5. 生成INSERT脚本，可用于数据迁移和备份
6. 支持中文字符（UTF-8编码）
7. 显示查询进度和导出进度

使用步骤：
---------
1. 双击运行 SqlToExcel.exe

2. 连接数据库：
   - 点击"连接数据库"按钮
   - 填写服务器地址（如：localhost、*************等）
   - 填写数据库名称
   - 选择身份验证方式：
     * Windows身份验证：使用当前登录用户身份
     * SQL Server身份验证：输入用户名和密码
   - 点击"测试连接"验证连接
   - 点击"确定"保存连接

3. 执行查询：
   - 在SQL查询框中输入或修改SELECT语句
   - 程序已预置示例查询，可根据需要修改
   - 点击"执行查询"运行SQL
   - 查询结果显示在下方表格中

4. 导出文件：
   - 查询成功后点击"导出文件"
   - 选择保存位置和文件名
   - 程序自动生成CSV文件
   - 可选择立即打开文件

5. 生成INSERT脚本：
   - 查询成功后点击"生成INSERT脚本"
   - 输入目标表名
   - 选择保存位置和文件名
   - 程序自动生成SQL脚本文件
   - 可选择立即打开文件

示例SQL（程序预置）：
------------------
SELECT 
    c.Content as '题库',
    a.QuestionContent as '题目',
    case when b.Content ='' then  '空' else b.Content end as '选项',
    b.answerFlag as '正确答案标识',
    c.QuestionBankID,
    a.ExaminationQuestionID,
    b.ExaminationQuestionDetailID
FROM ExaminationQuestion a
JOIN ExaminationQuestionDetail b 
    ON a.ExaminationQuestionID = b.ExaminationQuestionID
join QuestionBank c 
    on a.QuestionBankID = c.QuestionBankID
WHERE isnull(a.deleteFlag,'') = ''
  AND isnull(b.deleteFlag,'') = '' and isnull(c.deleteFlag,'')=''
  AND NOT EXISTS (
      SELECT 1
      FROM ExaminationQuestionDetail b2
      WHERE b2.ExaminationQuestionID = a.ExaminationQuestionID
        AND b2.deleteFlag = ''
        AND b2.answerFlag = 1
  )
order by a.ExaminationQuestionID,b.sort

INSERT脚本生成示例：
-------------------
基于上述查询结果，可以生成如下格式的INSERT脚本：

INSERT INTO [目标表名] ([题库], [题目], [选项], [正确答案标识], [QuestionBankID], [ExaminationQuestionID], [ExaminationQuestionDetailID])
VALUES
    (N'数据库基础', N'什么是主键？', N'唯一标识', 1, 1001, 2001, 3001),
    (N'数据库基础', N'什么是外键？', N'关联字段', 0, 1001, 2001, 3002),
    ...
GO

注意事项：
---------
1. 确保对目标数据库有SELECT查询权限
2. 大数据量查询时请耐心等待
3. 导出的CSV文件可直接用Excel打开
4. 生成的INSERT脚本可直接在SQL Server中执行
5. 程序支持中文，导出文件编码为UTF-8
6. 空字符串、0值、NULL值按要求严格区分显示
7. INSERT脚本会自动处理特殊字符转义和数据类型格式化

系统要求：
---------
- Windows 7 或更高版本
- .NET Framework 4.7.2 或更高版本
- SQL Server 数据库访问权限

故障排除：
---------
如果遇到连接问题：
1. 检查服务器地址和数据库名称
2. 确认SQL Server服务正在运行
3. 验证用户名密码正确
4. 检查网络连接和防火墙设置

如果遇到查询问题：
1. 检查SQL语法是否正确
2. 确认表名和字段名存在
3. 验证是否有查询权限

技术支持：
---------
如有问题请联系开发人员。

版本：v1.0.0
日期：2024年
