-- INSERT脚本
-- 表名: EMRSourceList
-- 生成时间: 2024-12-30 18:00:00
-- 记录数: 3
-- 配置信息:
-- 自动排除自增主键: 是
-- 默认值配置: 4 个字段
--   ModifyEmployeeID = '160579'
--   ModifyDateTime = GETDATE()
--   CreateEmployeeID = '160579'
--   CreateDateTime = GETDATE()

-- 批次 1
INSERT INTO [EMRSourceList] ([SourceName], [SourceType], [HospitalID], [ShowName], [Style], [CreateEmployeeID], [CreateDateTime], [ModifyEmployeeID], [ModifyDateTime], [IsActive])
VALUES
    (N'护理干预详情记录', N'R', 5, N'体位护理', N'R', '160579', GETDATE(), '160579', GETDATE(), 1),
    (N'患者体位监测表', N'R', 5, N'体位监测', N'R', '160579', GETDATE(), '160579', GETDATE(), 1),
    (N'护理评估记录单', N'R', 5, N'体位评估', N'R', '160579', GETDATE(), '160579', GETDATE(), 1);

GO

-- 脚本执行完成，共插入 3 条记录

-- 高级功能说明：
-- 1. 自动排除了自增主键字段 (如 ID, AutoID 等)
-- 2. ModifyEmployeeID 和 CreateEmployeeID 自动使用默认值 '160579'
-- 3. ModifyDateTime 和 CreateDateTime 自动使用 GETDATE() 函数
-- 4. 只包含用户选择的字段，排除了不需要的字段
-- 5. 支持自定义默认值配置，适应不同的业务需求
-- 6. 智能识别常见的主键字段模式进行自动排除
-- 7. 支持预设配置快速应用常用的默认值设置
