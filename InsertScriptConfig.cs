using System;
using System.Collections.Generic;
using System.Linq;

namespace SqlToExcel
{
    /// <summary>
    /// INSERT脚本生成配置类
    /// </summary>
    public class InsertScriptConfig
    {
        /// <summary>
        /// 字段默认值配置
        /// </summary>
        public Dictionary<string, string> DefaultValues { get; set; }

        /// <summary>
        /// 需要排除的字段（如自增主键）
        /// </summary>
        public HashSet<string> ExcludedColumns { get; set; }

        /// <summary>
        /// 主键字段名称（用于自动检测自增）
        /// </summary>
        public HashSet<string> PrimaryKeyColumns { get; set; }

        /// <summary>
        /// 是否自动排除自增主键
        /// </summary>
        public bool AutoExcludeIdentityColumns { get; set; }

        public InsertScriptConfig()
        {
            DefaultValues = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
            ExcludedColumns = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
            PrimaryKeyColumns = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
            AutoExcludeIdentityColumns = true;

            // 设置常用的默认值
            SetDefaultValues();
        }

        /// <summary>
        /// 设置常用的默认值配置
        /// </summary>
        private void SetDefaultValues()
        {
            // 常用的默认值配置
            DefaultValues["ModifyEmployeeID"] = "'160579'";
            DefaultValues["ModifyDateTime"] = "GETDATE()";
            DefaultValues["CreateEmployeeID"] = "'160579'";
            DefaultValues["CreateDateTime"] = "GETDATE()";
            DefaultValues["CreateTime"] = "GETDATE()";
            DefaultValues["ModifyTime"] = "GETDATE()";
            DefaultValues["UpdateTime"] = "GETDATE()";
            DefaultValues["LastUpdateTime"] = "GETDATE()";
            DefaultValues["CreatedBy"] = "'160579'";
            DefaultValues["ModifiedBy"] = "'160579'";
            DefaultValues["UpdatedBy"] = "'160579'";
            DefaultValues["CreatedDate"] = "GETDATE()";
            DefaultValues["ModifiedDate"] = "GETDATE()";
            DefaultValues["UpdatedDate"] = "GETDATE()";

            // 常见的自增主键字段名
            PrimaryKeyColumns.Add("ID");
            PrimaryKeyColumns.Add("Id");
            PrimaryKeyColumns.Add("id");
            PrimaryKeyColumns.Add("AutoID");
            PrimaryKeyColumns.Add("AutoId");
            PrimaryKeyColumns.Add("RowID");
            PrimaryKeyColumns.Add("RowId");
            PrimaryKeyColumns.Add("RecordID");
            PrimaryKeyColumns.Add("RecordId");
        }

        /// <summary>
        /// 添加默认值配置
        /// </summary>
        /// <param name="columnName">字段名</param>
        /// <param name="defaultValue">默认值</param>
        public void AddDefaultValue(string columnName, string defaultValue)
        {
            DefaultValues[columnName] = defaultValue;
        }

        /// <summary>
        /// 移除默认值配置
        /// </summary>
        /// <param name="columnName">字段名</param>
        public void RemoveDefaultValue(string columnName)
        {
            DefaultValues.Remove(columnName);
        }

        /// <summary>
        /// 添加排除字段
        /// </summary>
        /// <param name="columnName">字段名</param>
        public void AddExcludedColumn(string columnName)
        {
            ExcludedColumns.Add(columnName);
        }

        /// <summary>
        /// 移除排除字段
        /// </summary>
        /// <param name="columnName">字段名</param>
        public void RemoveExcludedColumn(string columnName)
        {
            ExcludedColumns.Remove(columnName);
        }

        /// <summary>
        /// 添加主键字段
        /// </summary>
        /// <param name="columnName">字段名</param>
        public void AddPrimaryKeyColumn(string columnName)
        {
            PrimaryKeyColumns.Add(columnName);
        }

        /// <summary>
        /// 检查字段是否应该被排除
        /// </summary>
        /// <param name="columnName">字段名</param>
        /// <returns>是否排除</returns>
        public bool ShouldExcludeColumn(string columnName)
        {
            // 明确配置的排除字段
            if (ExcludedColumns.Contains(columnName))
                return true;

            // 自动排除自增主键
            if (AutoExcludeIdentityColumns && IsLikelyIdentityColumn(columnName))
                return true;

            return false;
        }

        /// <summary>
        /// 检查字段是否有默认值配置
        /// </summary>
        /// <param name="columnName">字段名</param>
        /// <returns>是否有默认值</returns>
        public bool HasDefaultValue(string columnName)
        {
            return DefaultValues.ContainsKey(columnName);
        }

        /// <summary>
        /// 获取字段的默认值
        /// </summary>
        /// <param name="columnName">字段名</param>
        /// <returns>默认值</returns>
        public string GetDefaultValue(string columnName)
        {
            return DefaultValues.TryGetValue(columnName, out string value) ? value : null;
        }

        /// <summary>
        /// 判断字段是否可能是自增主键
        /// </summary>
        /// <param name="columnName">字段名</param>
        /// <returns>是否可能是自增主键</returns>
        private bool IsLikelyIdentityColumn(string columnName)
        {
            // 检查是否在预定义的主键字段列表中
            if (PrimaryKeyColumns.Contains(columnName))
                return true;

            // 检查字段名是否以ID结尾
            string upperColumnName = columnName.ToUpper();
            if (upperColumnName.EndsWith("ID") && upperColumnName.Length > 2)
            {
                // 排除一些明显不是自增主键的字段
                string[] nonIdentityPatterns = { "EMPLOYEEID", "USERID", "CUSTOMERID", "PRODUCTID", "CATEGORYID" };
                if (!nonIdentityPatterns.Any(pattern => upperColumnName.Contains(pattern)))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 获取配置摘要
        /// </summary>
        /// <returns>配置摘要字符串</returns>
        public string GetConfigSummary()
        {
            var summary = new List<string>();
            
            summary.Add($"自动排除自增主键: {(AutoExcludeIdentityColumns ? "是" : "否")}");
            
            if (DefaultValues.Count > 0)
            {
                summary.Add($"默认值配置: {DefaultValues.Count} 个字段");
                foreach (var kv in DefaultValues.Take(3))
                {
                    summary.Add($"  {kv.Key} = {kv.Value}");
                }
                if (DefaultValues.Count > 3)
                {
                    summary.Add($"  ... 还有 {DefaultValues.Count - 3} 个");
                }
            }

            if (ExcludedColumns.Count > 0)
            {
                summary.Add($"排除字段: {string.Join(", ", ExcludedColumns.Take(5))}");
                if (ExcludedColumns.Count > 5)
                {
                    summary.Add($"  ... 还有 {ExcludedColumns.Count - 5} 个");
                }
            }

            return string.Join("\n", summary);
        }
    }
}
