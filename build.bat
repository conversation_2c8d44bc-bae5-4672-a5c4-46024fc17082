@echo off
echo 正在编译 SQL查询导出Excel工具...

REM 设置MSBuild路径
set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"

REM 检查MSBuild是否存在
if not exist %MSBUILD_PATH% (
    echo 错误：找不到MSBuild.exe，请确保已安装Visual Studio 2022
    pause
    exit /b 1
)

REM 创建输出目录
if not exist "bin\Release" mkdir "bin\Release"

REM 编译项目
echo 正在编译项目...
%MSBUILD_PATH% SqlToExcel.csproj /p:Configuration=Release /p:Platform="Any CPU" /p:OutputPath=bin\Release\

if %ERRORLEVEL% EQU 0 (
    echo.
    echo 编译成功！
    echo 可执行文件位置：bin\Release\SqlToExcel.exe
    echo.
    echo 按任意键退出...
    pause >nul
) else (
    echo.
    echo 编译失败！请检查错误信息。
    echo.
    pause
)
