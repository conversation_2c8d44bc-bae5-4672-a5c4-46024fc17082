using System;
using System.Data;
using System.IO;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SqlToExcel
{
    public partial class MainForm : Form
    {
        private DatabaseHelper databaseHelper;
        private string connectionString;
        private InsertScriptConfig insertConfig;

        public MainForm()
        {
            InitializeComponent();
            this.Load += MainForm_Load;
            this.insertConfig = new InsertScriptConfig();
        }

        private void MainForm_Load(object sender, EventArgs e)
        {
            // 设置默认SQL示例
            txtSql.Text = @"SELECT 
    c.Content as '题库',
    a.QuestionContent as '题目',
    case when b.Content ='' then  '空' else b.Content end as '选项',
    b.answerFlag as '正确答案标识',
    c.QuestionBankID,
    a.ExaminationQuestionID,
    b.ExaminationQuestionDetailID
FROM ExaminationQuestion a
JOIN ExaminationQuestionDetail b 
    ON a.ExaminationQuestionID = b.ExaminationQuestionID
join QuestionBank c 
    on a.QuestionBankID = c.QuestionBankID
WHERE isnull(a.deleteFlag,'') = ''
  AND isnull(b.deleteFlag,'') = '' and isnull(c.deleteFlag,'')=''
  AND NOT EXISTS (
      SELECT 1
      FROM ExaminationQuestionDetail b2
      WHERE b2.ExaminationQuestionID = a.ExaminationQuestionID
        AND b2.deleteFlag = ''
        AND b2.answerFlag = 1
  )
order by a.ExaminationQuestionID,b.sort";

            // 初始状态禁用执行和导出按钮
            btnExecute.Enabled = false;
            btnExport.Enabled = false;
            btnGenerateInsert.Enabled = false;
        }

        private void btnConnect_Click(object sender, EventArgs e)
        {
            ConnectionForm connectionForm = new ConnectionForm();
            if (connectionForm.ShowDialog() == DialogResult.OK)
            {
                connectionString = connectionForm.ConnectionString;
                databaseHelper = new DatabaseHelper(connectionString);
                
                lblStatus.Text = "数据库连接成功";
                lblStatus.ForeColor = System.Drawing.Color.Green;
                btnExecute.Enabled = true;
                
                MessageBox.Show("数据库连接成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private async void btnExecute_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtSql.Text))
            {
                MessageBox.Show("请输入SQL查询语句！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                btnExecute.Enabled = false;
                lblStatus.Text = "正在执行查询...";
                lblStatus.ForeColor = System.Drawing.Color.Blue;
                progressBar.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;

                // 异步执行查询
                DataTable result = await Task.Run(() => databaseHelper.ExecuteQuery(txtSql.Text));
                
                // 显示结果
                dataGridView.DataSource = result;
                lblRecordCount.Text = $"共查询到 {result.Rows.Count} 条记录";
                
                lblStatus.Text = "查询执行完成";
                lblStatus.ForeColor = System.Drawing.Color.Green;
                btnExport.Enabled = true;
                btnGenerateInsert.Enabled = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"查询执行失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                lblStatus.Text = "查询执行失败";
                lblStatus.ForeColor = System.Drawing.Color.Red;
            }
            finally
            {
                btnExecute.Enabled = true;
                progressBar.Visible = false;
            }
        }

        private async void btnExport_Click(object sender, EventArgs e)
        {
            if (dataGridView.DataSource == null)
            {
                MessageBox.Show("没有可导出的数据！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            SaveFileDialog saveFileDialog = new SaveFileDialog();
            saveFileDialog.Filter = "CSV文件 (*.csv)|*.csv|Excel文件 (*.xlsx)|*.xlsx";
            saveFileDialog.DefaultExt = "csv";
            saveFileDialog.FileName = $"查询结果_{DateTime.Now:yyyyMMdd_HHmmss}.csv";

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    btnExport.Enabled = false;
                    lblStatus.Text = "正在导出文件...";
                    lblStatus.ForeColor = System.Drawing.Color.Blue;
                    progressBar.Visible = true;
                    progressBar.Style = ProgressBarStyle.Continuous;
                    progressBar.Value = 0;

                    DataTable dataTable = (DataTable)dataGridView.DataSource;
                    
                    // 异步导出Excel
                    await Task.Run(() => 
                    {
                        ExcelExporter.ExportToExcel(dataTable, saveFileDialog.FileName, (progress) =>
                        {
                            this.Invoke(new Action(() =>
                            {
                                progressBar.Value = progress;
                            }));
                        });
                    });

                    lblStatus.Text = "文件导出完成";
                    lblStatus.ForeColor = System.Drawing.Color.Green;

                    string fileType = Path.GetExtension(saveFileDialog.FileName).ToLower() == ".csv" ? "CSV" : "Excel";
                    DialogResult result = MessageBox.Show($"{fileType}文件导出成功！\n文件路径：{saveFileDialog.FileName}\n\n是否打开文件？",
                        "导出成功", MessageBoxButtons.YesNo, MessageBoxIcon.Information);
                    
                    if (result == DialogResult.Yes)
                    {
                        System.Diagnostics.Process.Start(saveFileDialog.FileName);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"文件导出失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    lblStatus.Text = "文件导出失败";
                    lblStatus.ForeColor = System.Drawing.Color.Red;
                }
                finally
                {
                    btnExport.Enabled = true;
                    progressBar.Visible = false;
                }
            }
        }

        private async void btnGenerateInsert_Click(object sender, EventArgs e)
        {
            if (dataGridView.DataSource == null)
            {
                MessageBox.Show("没有可生成脚本的数据！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            DataTable dataTable = (DataTable)dataGridView.DataSource;

            // 弹出配置对话框
            InsertConfigForm configForm = new InsertConfigForm(dataTable, insertConfig);
            if (configForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            // 更新配置
            insertConfig = configForm.Config;

            // 弹出表名输入对话框
            TableNameForm tableNameForm = new TableNameForm();
            if (tableNameForm.ShowDialog() != DialogResult.OK)
            {
                return;
            }

            string tableName = tableNameForm.TableName;

            SaveFileDialog saveFileDialog = new SaveFileDialog();
            saveFileDialog.Filter = "SQL脚本文件 (*.sql)|*.sql|文本文件 (*.txt)|*.txt";
            saveFileDialog.DefaultExt = "sql";
            saveFileDialog.FileName = $"INSERT_{tableName}_{DateTime.Now:yyyyMMdd_HHmmss}.sql";

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    btnGenerateInsert.Enabled = false;
                    lblStatus.Text = "正在生成INSERT脚本...";
                    lblStatus.ForeColor = System.Drawing.Color.Blue;
                    progressBar.Visible = true;
                    progressBar.Style = ProgressBarStyle.Continuous;
                    progressBar.Value = 0;

                    // 异步生成INSERT脚本
                    await Task.Run(() =>
                    {
                        InsertScriptGenerator.GenerateInsertScript(dataTable, tableName, saveFileDialog.FileName, (progress) =>
                        {
                            this.Invoke(new Action(() =>
                            {
                                progressBar.Value = progress;
                            }));
                        }, insertConfig);
                    });

                    lblStatus.Text = "INSERT脚本生成完成";
                    lblStatus.ForeColor = System.Drawing.Color.Green;

                    DialogResult result = MessageBox.Show($"INSERT脚本生成成功！\n文件路径：{saveFileDialog.FileName}\n\n是否打开文件？",
                        "生成成功", MessageBoxButtons.YesNo, MessageBoxIcon.Information);

                    if (result == DialogResult.Yes)
                    {
                        System.Diagnostics.Process.Start(saveFileDialog.FileName);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"INSERT脚本生成失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    lblStatus.Text = "INSERT脚本生成失败";
                    lblStatus.ForeColor = System.Drawing.Color.Red;
                }
                finally
                {
                    btnGenerateInsert.Enabled = true;
                    progressBar.Visible = false;
                }
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            txtSql.Clear();
            dataGridView.DataSource = null;
            lblRecordCount.Text = "";
            btnExport.Enabled = false;
            btnGenerateInsert.Enabled = false;
        }
    }
}
