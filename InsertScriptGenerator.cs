using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Text;

namespace SqlToExcel
{
    public class InsertScriptGenerator
    {
        /// <summary>
        /// 根据DataTable生成INSERT脚本
        /// </summary>
        /// <param name="dataTable">数据表</param>
        /// <param name="tableName">目标表名</param>
        /// <param name="filePath">保存文件路径</param>
        /// <param name="progressCallback">进度回调函数</param>
        /// <param name="config">INSERT脚本配置</param>
        public static void GenerateInsertScript(DataTable dataTable, string tableName, string filePath, Action<int> progressCallback = null, InsertScriptConfig config = null)
        {
            // 如果没有提供配置，使用默认配置
            if (config == null)
                config = new InsertScriptConfig();

            using (StreamWriter writer = new StreamWriter(filePath, false, Encoding.UTF8))
            {
                // 写入BOM以支持中文
                writer.Write('\uFEFF');

                // 写入文件头注释
                writer.WriteLine("-- INSERT脚本");
                writer.WriteLine($"-- 表名: {tableName}");
                writer.WriteLine($"-- 生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                writer.WriteLine($"-- 记录数: {dataTable.Rows.Count}");

                // 写入配置信息
                writer.WriteLine("-- 配置信息:");
                foreach (string line in config.GetConfigSummary().Split('\n'))
                {
                    writer.WriteLine($"-- {line}");
                }
                writer.WriteLine();

                if (dataTable.Rows.Count == 0)
                {
                    writer.WriteLine("-- 没有数据需要插入");
                    progressCallback?.Invoke(100);
                    return;
                }

                // 筛选要包含的列
                var includedColumns = new List<DataColumn>();
                var includedColumnNames = new List<string>();

                foreach (DataColumn column in dataTable.Columns)
                {
                    if (!config.ShouldExcludeColumn(column.ColumnName))
                    {
                        includedColumns.Add(column);
                        includedColumnNames.Add($"[{column.ColumnName}]");
                    }
                }

                if (includedColumns.Count == 0)
                {
                    writer.WriteLine("-- 所有字段都被排除，没有数据需要插入");
                    progressCallback?.Invoke(100);
                    return;
                }

                string columnList = string.Join(", ", includedColumnNames);

                // 生成INSERT语句
                int totalRows = dataTable.Rows.Count;
                int batchSize = 1000; // 每1000条记录一个批次
                int currentBatch = 0;

                for (int row = 0; row < totalRows; row++)
                {
                    if (row % batchSize == 0)
                    {
                        if (row > 0)
                        {
                            writer.WriteLine();
                            writer.WriteLine("GO");
                            writer.WriteLine();
                        }
                        
                        currentBatch++;
                        writer.WriteLine($"-- 批次 {currentBatch}");
                        writer.WriteLine($"INSERT INTO [{tableName}] ({columnList})");
                        writer.WriteLine("VALUES");
                    }

                    // 生成值列表（只包含未排除的列）
                    string[] values = new string[includedColumns.Count];
                    for (int col = 0; col < includedColumns.Count; col++)
                    {
                        DataColumn column = includedColumns[col];
                        object value = dataTable.Rows[row][column];

                        // 检查是否有默认值配置
                        if (config.HasDefaultValue(column.ColumnName))
                        {
                            values[col] = config.GetDefaultValue(column.ColumnName);
                        }
                        else
                        {
                            values[col] = FormatSqlValue(value, column.DataType);
                        }
                    }

                    string valueList = "(" + string.Join(", ", values) + ")";
                    
                    // 判断是否是批次中的最后一行或者总的最后一行
                    bool isLastInBatch = (row + 1) % batchSize == 0 || row == totalRows - 1;
                    
                    if (isLastInBatch)
                    {
                        writer.WriteLine($"    {valueList};");
                    }
                    else
                    {
                        writer.WriteLine($"    {valueList},");
                    }

                    // 报告进度
                    if (progressCallback != null && row % 100 == 0)
                    {
                        int progress = (int)((double)(row + 1) / totalRows * 100);
                        progressCallback(progress);
                    }
                }

                writer.WriteLine();
                writer.WriteLine("GO");
                writer.WriteLine();
                writer.WriteLine($"-- 脚本执行完成，共插入 {totalRows} 条记录");

                // 完成进度
                progressCallback?.Invoke(100);
            }
        }

        /// <summary>
        /// 格式化SQL值
        /// </summary>
        /// <param name="value">原始值</param>
        /// <param name="dataType">数据类型</param>
        /// <returns>格式化后的SQL值</returns>
        private static string FormatSqlValue(object value, Type dataType)
        {
            if (value == null || value == DBNull.Value)
            {
                return "NULL";
            }

            // 字符串类型
            if (dataType == typeof(string) || value is string)
            {
                string stringValue = value.ToString();
                if (string.IsNullOrEmpty(stringValue))
                {
                    return "''"; // 空字符串
                }
                // 转义单引号
                return "N'" + stringValue.Replace("'", "''") + "'";
            }

            // 日期时间类型
            if (dataType == typeof(DateTime) || value is DateTime)
            {
                DateTime dateValue = (DateTime)value;
                return "'" + dateValue.ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";
            }

            // 布尔类型
            if (dataType == typeof(bool) || value is bool)
            {
                return ((bool)value) ? "1" : "0";
            }

            // 数字类型
            if (IsNumericType(dataType))
            {
                return value.ToString();
            }

            // GUID类型
            if (dataType == typeof(Guid) || value is Guid)
            {
                return "'" + value.ToString() + "'";
            }

            // 二进制数据
            if (dataType == typeof(byte[]) || value is byte[])
            {
                byte[] bytes = (byte[])value;
                if (bytes.Length == 0)
                {
                    return "NULL";
                }
                StringBuilder hex = new StringBuilder("0x");
                foreach (byte b in bytes)
                {
                    hex.AppendFormat("{0:X2}", b);
                }
                return hex.ToString();
            }

            // 其他类型，转换为字符串并加引号
            return "N'" + value.ToString().Replace("'", "''") + "'";
        }

        /// <summary>
        /// 判断是否为数字类型
        /// </summary>
        /// <param name="type">数据类型</param>
        /// <returns>是否为数字类型</returns>
        private static bool IsNumericType(Type type)
        {
            return type == typeof(int) || type == typeof(long) || type == typeof(short) || 
                   type == typeof(byte) || type == typeof(uint) || type == typeof(ulong) || 
                   type == typeof(ushort) || type == typeof(sbyte) || type == typeof(decimal) || 
                   type == typeof(double) || type == typeof(float);
        }
    }
}
