using System;
using System.Data;
using System.Data.SqlClient;

namespace SqlToExcel
{
    public class DatabaseHelper
    {
        private string connectionString;

        public DatabaseHelper(string connectionString)
        {
            this.connectionString = connectionString;
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        /// <returns>连接是否成功</returns>
        public bool TestConnection()
        {
            try
            {
                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 执行SQL查询并返回DataTable
        /// </summary>
        /// <param name="sql">SQL查询语句</param>
        /// <returns>查询结果DataTable</returns>
        public DataTable ExecuteQuery(string sql)
        {
            DataTable dataTable = new DataTable();
            
            using (SqlConnection connection = new SqlConnection(connectionString))
            {
                connection.Open();
                using (SqlCommand command = new SqlCommand(sql, connection))
                {
                    using (SqlDataAdapter adapter = new SqlDataAdapter(command))
                    {
                        adapter.Fill(dataTable);
                    }
                }
            }
            
            return dataTable;
        }

        /// <summary>
        /// 构建连接字符串
        /// </summary>
        /// <param name="server">服务器地址</param>
        /// <param name="database">数据库名</param>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <param name="integratedSecurity">是否使用Windows身份验证</param>
        /// <returns>连接字符串</returns>
        public static string BuildConnectionString(string server, string database, string username, string password, bool integratedSecurity)
        {
            if (integratedSecurity)
            {
                return $"Server={server};Database={database};Integrated Security=true;";
            }
            else
            {
                return $"Server={server};Database={database};User Id={username};Password={password};";
            }
        }
    }
}
