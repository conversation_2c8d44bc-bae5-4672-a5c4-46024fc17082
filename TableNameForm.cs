using System;
using System.Windows.Forms;

namespace SqlToExcel
{
    public partial class TableNameForm : Form
    {
        public string TableName { get; private set; }

        public TableNameForm()
        {
            InitializeComponent();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtTableName.Text))
            {
                MessageBox.Show("请输入表名！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtTableName.Focus();
                return;
            }

            TableName = txtTableName.Text.Trim();
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void TableNameForm_Load(object sender, EventArgs e)
        {
            txtTableName.Focus();
        }
    }
}
