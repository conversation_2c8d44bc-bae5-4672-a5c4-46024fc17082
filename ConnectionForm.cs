using System;
using System.Windows.Forms;

namespace SqlToExcel
{
    public partial class ConnectionForm : Form
    {
        public string ConnectionString { get; private set; }

        public ConnectionForm()
        {
            InitializeComponent();
        }

        private void btnTest_Click(object sender, EventArgs e)
        {
            try
            {
                string connStr = DatabaseHelper.BuildConnectionString(
                    txtServer.Text,
                    txtDatabase.Text,
                    txtUsername.Text,
                    txtPassword.Text,
                    chkIntegratedSecurity.Checked
                );

                DatabaseHelper dbHelper = new DatabaseHelper(connStr);
                if (dbHelper.TestConnection())
                {
                    MessageBox.Show("连接成功！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("连接失败，请检查连接参数！", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"连接测试出错：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtServer.Text))
            {
                MessageBox.Show("请输入服务器地址！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtServer.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtDatabase.Text))
            {
                MessageBox.Show("请输入数据库名称！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtDatabase.Focus();
                return;
            }

            if (!chkIntegratedSecurity.Checked)
            {
                if (string.IsNullOrWhiteSpace(txtUsername.Text))
                {
                    MessageBox.Show("请输入用户名！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtUsername.Focus();
                    return;
                }
            }

            ConnectionString = DatabaseHelper.BuildConnectionString(
                txtServer.Text,
                txtDatabase.Text,
                txtUsername.Text,
                txtPassword.Text,
                chkIntegratedSecurity.Checked
            );

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void chkIntegratedSecurity_CheckedChanged(object sender, EventArgs e)
        {
            bool enabled = !chkIntegratedSecurity.Checked;
            txtUsername.Enabled = enabled;
            txtPassword.Enabled = enabled;
            lblUsername.Enabled = enabled;
            lblPassword.Enabled = enabled;
        }
    }
}
