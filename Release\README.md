# SQL 查询导出 Excel 工具

这是一个通用的 Windows 桌面应用程序，可以连接 SQL Server 数据库，执行任意 SELECT 查询并将结果导出为 CSV 文件或生成 INSERT 脚本。

## 功能特点

- **通用 SQL 查询支持**：支持执行任何 SELECT 语句
- **数据库连接管理**：支持 SQL Server 身份验证和 Windows 身份验证
- **数据格式保持**：
  - 空字符串显示为空
  - 数字 0 显示为 0
  - NULL 值显示为"NULL"
- **CSV 文件导出**：生成可用 Excel 打开的 CSV 文件
- **INSERT 脚本生成**：根据查询结果生成标准 SQL INSERT 脚本
- **进度显示**：导出大量数据时显示进度条
- **用户友好界面**：直观的 WinForms 界面

## 系统要求

- Windows 7 或更高版本
- .NET Framework 4.7.2 或更高版本
- SQL Server 数据库访问权限

## 使用方法

### 1. 连接数据库

- 启动程序后，点击"连接数据库"按钮
- 填写数据库连接信息：
  - 服务器地址（如：localhost 或 *************）
  - 数据库名称
  - 选择身份验证方式：
    - Windows 身份验证：使用当前 Windows 用户身份
    - SQL Server 身份验证：输入用户名和密码
- 点击"测试连接"验证连接是否成功
- 点击"确定"保存连接配置

### 2. 执行 SQL 查询

- 在 SQL 查询框中输入 SELECT 语句
- 程序已预置了一个示例查询，可以根据需要修改
- 点击"执行查询"按钮运行 SQL
- 查询结果将显示在下方的数据表格中

### 3. 导出数据

有两种导出方式：

**导出 CSV 文件：**

- 查询成功后点击"导出文件"按钮
- 选择保存位置和文件名
- 程序自动生成 CSV 文件（可用 Excel 打开）

**生成 INSERT 脚本：**

- 查询成功后点击"生成 INSERT 脚本"按钮
- 输入目标表名
- 选择保存位置和文件名
- 程序自动生成 SQL 脚本文件

## 示例 SQL 查询

程序预置了以下示例查询：

```sql
SELECT
    c.Content as '题库',
    a.QuestionContent as '题目',
    case when b.Content ='' then  '空' else b.Content end as '选项',
    b.answerFlag as '正确答案标识',
    c.QuestionBankID,
    a.ExaminationQuestionID,
    b.ExaminationQuestionDetailID
FROM ExaminationQuestion a
JOIN ExaminationQuestionDetail b
    ON a.ExaminationQuestionID = b.ExaminationQuestionID
join QuestionBank c
    on a.QuestionBankID = c.QuestionBankID
WHERE isnull(a.deleteFlag,'') = ''
  AND isnull(b.deleteFlag,'') = '' and isnull(c.deleteFlag,'')=''
  AND NOT EXISTS (
      SELECT 1
      FROM ExaminationQuestionDetail b2
      WHERE b2.ExaminationQuestionID = a.ExaminationQuestionID
        AND b2.deleteFlag = ''
        AND b2.answerFlag = 1
  )
order by a.ExaminationQuestionID,b.sort
```

## 编译说明

### 开发环境

- Visual Studio 2017 或更高版本
- .NET Framework 4.7.2

### 依赖包

- EPPlus 4.5.3.3（Excel 操作库）
- System.Data.SqlClient（SQL Server 连接）

### 编译步骤

1. 使用 Visual Studio 打开 `SqlToExcel.csproj`
2. 还原 NuGet 包（EPPlus）
3. 生成解决方案
4. 在 `bin\Release` 目录下找到生成的 exe 文件

### 手动编译（使用 MSBuild）

```cmd
# 还原包
nuget restore

# 编译Release版本
msbuild SqlToExcel.csproj /p:Configuration=Release
```

## 注意事项

1. **数据类型处理**：程序会根据数据库字段类型自动处理 Excel 中的显示格式
2. **大数据量**：处理大量数据时请耐心等待，程序会显示进度条
3. **权限要求**：确保对目标数据库有 SELECT 权限
4. **文件路径**：导出 Excel 时确保目标路径有写入权限

## 故障排除

### 连接失败

- 检查服务器地址和端口是否正确
- 确认数据库服务是否运行
- 验证用户名密码是否正确
- 检查防火墙设置

### 查询错误

- 检查 SQL 语法是否正确
- 确认表名和字段名是否存在
- 验证是否有足够的查询权限

### 导出失败

- 确认目标路径有写入权限
- 检查磁盘空间是否充足
- 确保 Excel 文件未被其他程序占用

## 版本历史

- v1.0.0：初始版本，支持基本的 SQL 查询和 Excel 导出功能
