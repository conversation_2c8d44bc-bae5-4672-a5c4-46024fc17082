# SQL查询导出Excel工具 - 功能更新报告

## 新增功能：INSERT脚本生成

### 📋 功能概述
根据用户需求，已成功添加了INSERT脚本生成功能。现在用户可以根据查询结果生成标准的SQL INSERT脚本，用于数据迁移、备份或复制。

### ✅ 新增功能详情

#### 1. INSERT脚本生成器
- **文件**: `InsertScriptGenerator.cs`
- **功能**: 将DataTable数据转换为标准SQL INSERT语句
- **特性**:
  - 自动数据类型处理和格式化
  - 特殊字符转义（单引号、换行符等）
  - Unicode字符串支持（N''前缀）
  - NULL值正确处理
  - 大数据量分批处理（每1000条一个批次）
  - 进度回调支持

#### 2. 表名输入对话框
- **文件**: `TableNameForm.cs` 和 `TableNameForm.Designer.cs`
- **功能**: 用户友好的表名输入界面
- **特性**:
  - 简洁的对话框设计
  - 输入验证
  - 支持回车键快速确认

#### 3. 主界面增强
- **新增按钮**: "生成INSERT脚本"
- **位置**: 查询结果区域，与"导出文件"按钮并列
- **状态管理**: 只有在查询成功后才启用

### 🔧 技术实现

#### 数据类型处理
```csharp
// 字符串类型 - 使用N''前缀支持Unicode
if (dataType == typeof(string))
    return "N'" + stringValue.Replace("'", "''") + "'";

// 日期时间类型 - 标准SQL格式
if (dataType == typeof(DateTime))
    return "'" + dateValue.ToString("yyyy-MM-dd HH:mm:ss.fff") + "'";

// 数字类型 - 直接输出
if (IsNumericType(dataType))
    return value.ToString();

// NULL值处理
if (value == null || value == DBNull.Value)
    return "NULL";
```

#### 批次处理
- 每1000条记录为一个批次
- 使用`GO`语句分隔批次
- 避免单个事务过大导致的性能问题

#### 文件格式
```sql
-- INSERT脚本
-- 表名: TableName
-- 生成时间: 2024-12-30 15:30:00
-- 记录数: 1000

-- 批次 1
INSERT INTO [TableName] ([Column1], [Column2], [Column3])
VALUES
    (N'Value1', 123, '2024-01-01 10:00:00.000'),
    (N'Value2', 456, '2024-01-01 11:00:00.000'),
    ...
GO
```

### 📁 新增文件

1. **InsertScriptGenerator.cs** - INSERT脚本生成核心类
2. **TableNameForm.cs** - 表名输入窗体逻辑
3. **TableNameForm.Designer.cs** - 表名输入窗体设计
4. **TableNameForm.resx** - 表名输入窗体资源
5. **INSERT脚本示例.sql** - 功能演示示例

### 🎯 使用场景

#### 示例场景：数据迁移
用户提供的查询示例：
```sql
SELECT * FROM EMRSourceList 
WHERE SourceID IN(
    SELECT NursingInterventionDetailID 
    FROM NursingInterventionDetail 
    WHERE NursingInterventionMainID ='6010' 
      AND HospitalID = 5 
      AND ShowName LIKE '%位%' 
      AND Style='R'
)
```

生成的INSERT脚本可以直接在目标数据库中执行，实现数据的精确复制。

### 🔄 工作流程

1. **执行查询** - 用户输入SELECT语句并执行
2. **点击按钮** - 点击"生成INSERT脚本"按钮
3. **输入表名** - 在弹出对话框中输入目标表名
4. **选择保存** - 选择脚本文件保存位置
5. **生成脚本** - 程序异步生成INSERT脚本
6. **完成提示** - 显示完成消息，可选择打开文件

### 📊 性能特性

- **异步处理**: 不阻塞用户界面
- **进度显示**: 实时显示生成进度
- **内存优化**: 流式写入，适合大数据量
- **错误处理**: 完整的异常处理机制

### 🎨 用户体验

- **一致性**: 与现有导出功能保持一致的操作流程
- **直观性**: 清晰的按钮标识和状态提示
- **便捷性**: 支持键盘快捷操作
- **反馈性**: 详细的进度和状态信息

### 📝 文档更新

1. **使用说明.txt** - 添加INSERT脚本生成使用方法
2. **README.md** - 更新功能特点和使用说明
3. **项目文件** - 更新编译配置包含新文件

### ✅ 测试状态

- [x] 编译成功
- [x] 界面显示正常
- [x] 按钮状态管理正确
- [x] 对话框功能正常
- [x] 文件生成功能完整

### 🚀 部署状态

- [x] 可执行文件已更新
- [x] 发布包已更新
- [x] 文档已同步更新

## 总结

INSERT脚本生成功能已成功集成到现有系统中，为用户提供了强大的数据迁移和备份能力。该功能完全符合用户需求，支持单表数据的精确复制，是对原有CSV导出功能的重要补充。

用户现在可以：
1. 执行复杂的SELECT查询
2. 将结果导出为CSV文件（Excel兼容）
3. 生成标准SQL INSERT脚本用于数据迁移

这使得工具的实用性和通用性得到了显著提升。
