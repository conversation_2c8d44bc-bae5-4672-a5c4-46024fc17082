using System;
using System.Data;
using System.Linq;
using System.Windows.Forms;

namespace SqlToExcel
{
    public partial class InsertConfigForm : Form
    {
        public InsertScriptConfig Config { get; private set; }
        private DataTable dataTable;

        public InsertConfigForm(DataTable dataTable, InsertScriptConfig config = null)
        {
            InitializeComponent();
            this.dataTable = dataTable;
            this.Config = config ?? new InsertScriptConfig();
            
            InitializeData();
        }

        private void InitializeData()
        {
            // 设置自增主键排除选项
            chkAutoExcludeIdentity.Checked = Config.AutoExcludeIdentityColumns;

            // 初始化字段列表
            LoadColumnList();

            // 初始化默认值列表
            LoadDefaultValuesList();
        }

        private void LoadColumnList()
        {
            lstColumns.Items.Clear();
            
            foreach (DataColumn column in dataTable.Columns)
            {
                var item = new ListViewItem(column.ColumnName);
                item.SubItems.Add(column.DataType.Name);
                
                // 检查是否被排除
                bool isExcluded = Config.ShouldExcludeColumn(column.ColumnName);
                item.SubItems.Add(isExcluded ? "是" : "否");
                
                // 检查是否有默认值
                bool hasDefault = Config.HasDefaultValue(column.ColumnName);
                item.SubItems.Add(hasDefault ? Config.GetDefaultValue(column.ColumnName) : "");
                
                item.Checked = !isExcluded; // 未排除的字段被选中
                item.Tag = column.ColumnName;
                
                lstColumns.Items.Add(item);
            }
        }

        private void LoadDefaultValuesList()
        {
            lstDefaultValues.Items.Clear();
            
            foreach (var kv in Config.DefaultValues)
            {
                var item = new ListViewItem(kv.Key);
                item.SubItems.Add(kv.Value);
                item.Tag = kv.Key;
                lstDefaultValues.Items.Add(item);
            }
        }

        private void chkAutoExcludeIdentity_CheckedChanged(object sender, EventArgs e)
        {
            Config.AutoExcludeIdentityColumns = chkAutoExcludeIdentity.Checked;
            LoadColumnList(); // 重新加载列表以反映变化
        }

        private void btnAddDefault_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtColumnName.Text) || string.IsNullOrWhiteSpace(txtDefaultValue.Text))
            {
                MessageBox.Show("请输入字段名和默认值！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            string columnName = txtColumnName.Text.Trim();
            string defaultValue = txtDefaultValue.Text.Trim();

            Config.AddDefaultValue(columnName, defaultValue);
            LoadDefaultValuesList();
            LoadColumnList(); // 更新字段列表显示

            // 清空输入框
            txtColumnName.Clear();
            txtDefaultValue.Clear();
        }

        private void btnRemoveDefault_Click(object sender, EventArgs e)
        {
            if (lstDefaultValues.SelectedItems.Count == 0)
            {
                MessageBox.Show("请选择要删除的默认值配置！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedItem = lstDefaultValues.SelectedItems[0];
            string columnName = selectedItem.Tag.ToString();

            Config.RemoveDefaultValue(columnName);
            LoadDefaultValuesList();
            LoadColumnList(); // 更新字段列表显示
        }

        private void lstColumns_ItemChecked(object sender, ItemCheckedEventArgs e)
        {
            string columnName = e.Item.Tag.ToString();
            
            if (e.Item.Checked)
            {
                // 字段被选中，从排除列表中移除
                Config.RemoveExcludedColumn(columnName);
            }
            else
            {
                // 字段被取消选中，添加到排除列表
                Config.AddExcludedColumn(columnName);
            }

            // 更新显示
            LoadColumnList();
        }

        private void lstDefaultValues_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (lstDefaultValues.SelectedItems.Count > 0)
            {
                var selectedItem = lstDefaultValues.SelectedItems[0];
                txtColumnName.Text = selectedItem.SubItems[0].Text;
                txtDefaultValue.Text = selectedItem.SubItems[1].Text;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void btnPreset_Click(object sender, EventArgs e)
        {
            // 添加预设的默认值配置
            var presetForm = new PresetConfigForm();
            if (presetForm.ShowDialog() == DialogResult.OK)
            {
                foreach (var kv in presetForm.SelectedPresets)
                {
                    Config.AddDefaultValue(kv.Key, kv.Value);
                }
                LoadDefaultValuesList();
                LoadColumnList();
            }
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("确定要重置所有配置吗？", "确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                Config = new InsertScriptConfig();
                InitializeData();
            }
        }
    }
}
