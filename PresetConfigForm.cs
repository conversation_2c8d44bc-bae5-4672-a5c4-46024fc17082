using System;
using System.Collections.Generic;
using System.Windows.Forms;

namespace SqlToExcel
{
    public partial class PresetConfigForm : Form
    {
        public Dictionary<string, string> SelectedPresets { get; private set; }

        public PresetConfigForm()
        {
            InitializeComponent();
            SelectedPresets = new Dictionary<string, string>();
            LoadPresets();
        }

        private void LoadPresets()
        {
            // 预设的常用默认值配置
            var presets = new Dictionary<string, string>
            {
                // 员工ID相关
                ["ModifyEmployeeID"] = "'160579'",
                ["CreateEmployeeID"] = "'160579'",
                ["CreatedBy"] = "'160579'",
                ["ModifiedBy"] = "'160579'",
                ["UpdatedBy"] = "'160579'",
                ["OperatorID"] = "'160579'",
                ["UserID"] = "'160579'",

                // 时间相关
                ["ModifyDateTime"] = "GETDATE()",
                ["CreateDateTime"] = "GETDATE()",
                ["CreateTime"] = "GETDATE()",
                ["ModifyTime"] = "GETDATE()",
                ["UpdateTime"] = "GETDATE()",
                ["LastUpdateTime"] = "GETDATE()",
                ["CreatedDate"] = "GETDATE()",
                ["ModifiedDate"] = "GETDATE()",
                ["UpdatedDate"] = "GETDATE()",
                ["OperateTime"] = "GETDATE()",
                ["RecordTime"] = "GETDATE()",

                // 状态相关
                ["IsActive"] = "1",
                ["IsDeleted"] = "0",
                ["DeleteFlag"] = "''",
                ["Status"] = "1",
                ["IsEnabled"] = "1",
                ["IsValid"] = "1",

                // 版本相关
                ["Version"] = "1",
                ["RowVersion"] = "1",

                // 其他常用
                ["Remark"] = "''",
                ["Description"] = "''",
                ["Note"] = "''",
                ["Comment"] = "''"
            };

            lstPresets.Items.Clear();
            foreach (var preset in presets)
            {
                var item = new ListViewItem(preset.Key);
                item.SubItems.Add(preset.Value);
                item.Tag = preset;
                lstPresets.Items.Add(item);
            }
        }

        private void btnSelectAll_Click(object sender, EventArgs e)
        {
            foreach (ListViewItem item in lstPresets.Items)
            {
                item.Checked = true;
            }
        }

        private void btnSelectNone_Click(object sender, EventArgs e)
        {
            foreach (ListViewItem item in lstPresets.Items)
            {
                item.Checked = false;
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            SelectedPresets.Clear();
            
            foreach (ListViewItem item in lstPresets.Items)
            {
                if (item.Checked)
                {
                    var preset = (KeyValuePair<string, string>)item.Tag;
                    SelectedPresets[preset.Key] = preset.Value;
                }
            }

            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
